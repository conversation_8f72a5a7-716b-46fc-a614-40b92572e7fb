# Use Node.js 18 LTS as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --only=production

# Copy source code
COPY src/ ./src/

# Create uploads directory structure
RUN mkdir -p uploads/assignments

# Expose port
EXPOSE 9005

# Set environment to development to enable auto-sync
ENV NODE_ENV=development

# Create startup script
COPY <<EOF /app/start.sh
#!/bin/sh
echo "Waiting for database to be ready..."
sleep 10

echo "Initializing database..."
node src/scripts/init-database.js

echo "Starting application..."
npm start
EOF

RUN chmod +x /app/start.sh

# Start the application
CMD ["/app/start.sh"]
