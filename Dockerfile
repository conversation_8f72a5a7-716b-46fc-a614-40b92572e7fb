# Use Node.js 18 LTS as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --only=production

# Copy source code
COPY src/ ./src/

# Create uploads directory structure
RUN mkdir -p uploads/assignments

# Expose port
EXPOSE 9005

# Set environment to production
ENV NODE_ENV=production

# Start the application
CMD ["npm", "start"]
