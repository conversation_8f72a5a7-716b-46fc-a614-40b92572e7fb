const { AppDataSource } = require('../config/database');

async function initializeDatabase() {
  try {
    console.log('Connecting to database...');
    await AppDataSource.initialize();
    
    console.log('Database connected successfully');
    
    // Synchronize database schema
    console.log('Synchronizing database schema...');
    await AppDataSource.synchronize();
    
    console.log('Database schema synchronized successfully');
    
    // Create default admin user if not exists
    const userRepository = AppDataSource.getRepository('User');
    const adminExists = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminExists) {
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const admin = userRepository.create({
        name: 'Administrator',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        role: 'admin',
        is_active: true
      });
      
      await userRepository.save(admin);
      console.log('Default admin user created: <EMAIL> / admin123');
    }
    
    console.log('Database initialization completed');
    process.exit(0);
  } catch (error) {
    console.error('Database initialization failed:', error);
    process.exit(1);
  }
}

initializeDatabase();
