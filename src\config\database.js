const { DataSource } = require('typeorm');
require('dotenv').config();

// Import entities
const User = require('../entities/User');
const Course = require('../entities/Course');
const Classroom = require('../entities/Classroom');
const ClassStudent = require('../entities/ClassStudent');
const Session = require('../entities/Session');
const Attendance = require('../entities/Attendance');
const TuitionLog = require('../entities/TuitionLog');
const TuitionDetail = require('../entities/TuitionDetail');
const Enrollment = require('../entities/Enrollment');
// const Assignment = require('../entities/AssignmentEntity');
// const AssignmentSubmission = require('../entities/AssignmentSubmissionEntity');

const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'student_management',
  synchronize: true, // Auto-sync enabled for Docker
  logging: process.env.NODE_ENV === 'development',
  entities: [
    User,
    Course,
    Classroom,
    ClassStudent,
    Enrollment,
    Session,
    Attendance,
    TuitionLog,
    TuitionDetail
    // Assignment,
    // AssignmentSubmission
  ],
  migrations: ['src/migrations/*.js'],
  subscribers: ['src/subscribers/*.js'],
});

module.exports = { AppDataSource };
