services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cms_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: student_management
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "9007:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - cms_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d student_management"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cms_backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 9005
      
      # Database configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres123
      DB_NAME: student_management
      
      # JWT configuration
      JWT_SECRET: vuquangduy-student-management-system-2025-super-secret-key
      JWT_EXPIRES_IN: 24h
      
      # CORS configuration - Allow all origins
      CORS_ORIGIN: "*"
      
      # Rate limiting
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      
      # File upload settings
      UPLOAD_DIR: uploads
      MAX_FILE_SIZE: 10485760
      ALLOWED_FILE_TYPES: jpg,jpeg,png,pdf,doc,docx

      # Cloudinary configuration
      CLOUDINARY_CLOUD_NAME: dpq72af5z
      CLOUDINARY_API_KEY: 337936265624194
      CLOUDINARY_API_SECRET: 8I3I7o0py8J_4IEUWfWAMNQ6WVw
    ports:
      - "9005:9005"
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - cms_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local

networks:
  cms_network:
    driver: bridge
